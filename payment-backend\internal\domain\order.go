package domain

import (
	"time"

	"payment-backend/internal/middleware"
)

// Order 订单实体
type Order struct {
	ID                uint64     `json:"id"`
	OrderID           string     `json:"order_id"`
	UserID            string     `json:"user_id"`
	ProductID         string     `json:"product_id"`
	ProductDesc       string     `json:"product_desc"`
	PriceID           string     `json:"price_id"`
	Quantity          uint32     `json:"quantity"`
	Amount            float64    `json:"amount"`
	NetAmount         float64    `json:"net_amount"`
	Currency          string     `json:"currency"`
	PayStatus         string     `json:"pay_status"`
	PayedMethod       string     `json:"payed_method"`
	PSPProvider       string     `json:"psp_provider"`
	CardNumber        string     `json:"card_number"`
	PayedAt           *time.Time `json:"payed_at,omitempty"`
	RefundStatus      string     `json:"refund_status"`
	RefundedAt        *time.Time `json:"refunded_at,omitempty"`
	PSPProductID      string     `json:"psp_product_id"`
	PSPProductDesc    string     `json:"psp_product_desc"`
	PSPPriceID        string     `json:"psp_price_id"`
	PSPPaymentID      string     `json:"psp_payment_id"`
	PSPCustomerID     string     `json:"psp_customer_id"`
	PSPCustomerEmail  string     `json:"psp_customer_email"`
	PSPSubscriptionID string     `json:"psp_subscription_id"`
	CreatedAt         time.Time  `json:"created_at"`
	UpdatedAt         time.Time  `json:"updated_at"`
	Deleted           bool       `json:"deleted"`
	DeletedAt         *time.Time `json:"deleted_at,omitempty"`
}

// 支付状态常量
const (
	PayStatusCreated   = "created"
	PayStatusPaid      = "paid"
	PayStatusSucceeded = "succeeded"
	PayStatusFailed    = "failed"
	PayStatusExpired   = "expired"
	PayStatusCancelled = "cancelled"
)

// 退款状态常量
const (
	RefundStatusNone      = "none"
	RefundStatusRequested = "requested"
	RefundStatusSucceeded = "succeeded"
	RefundStatusFailed    = "failed"
)

// 支付方式常量
const (
	PayMethodStripe = "stripe"
	PayMethodPayPal = "paypal"
	PayMethodCard   = "card"
	PayMethodWallet = "wallet"
)

// CreateOrderRequest 创建订单请求
type CreateOrderRequest struct {
	ProductID   string                 `json:"product_id" binding:"required"`
	ProductDesc string                 `json:"product_desc"`
	PriceID     string                 `json:"price_id" binding:"required"`
	Quantity    uint32                 `json:"quantity" binding:"required,min=1"`
	Currency    string                 `json:"currency" binding:"required"`
	PayedMethod string                 `json:"payed_method" binding:"required"`
	PSPProvider string                 `json:"psp_provider" binding:"required"`
	Description string                 `json:"description"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// CreateOrderResponse 创建订单响应
type CreateOrderResponse struct {
	OrderID     string    `json:"order_id"`
	CheckoutURL string    `json:"checkout_url"`
	Amount      float64   `json:"amount"`
	Currency    string    `json:"currency"`
	ExpiresAt   time.Time `json:"expires_at"`
}

// UpdateOrderRequest 更新订单请求
type UpdateOrderRequest struct {
	PayStatus         string     `json:"pay_status,omitempty"`
	PayedMethod       string     `json:"payed_method,omitempty"`
	CardNumber        string     `json:"card_number,omitempty"`
	PayedAt           *time.Time `json:"payed_at,omitempty"`
	RefundStatus      string     `json:"refund_status,omitempty"`
	RefundedAt        *time.Time `json:"refunded_at,omitempty"`
	PSPPaymentID      string     `json:"psp_payment_id,omitempty"`
	PSPCustomerID     string     `json:"psp_customer_id,omitempty"`
	PSPCustomerEmail  string     `json:"psp_customer_email,omitempty"`
	PSPSubscriptionID string     `json:"psp_subscription_id,omitempty"`
	NetAmount         *float64   `json:"net_amount,omitempty"`
}

// OrderRepository 订单仓储接口
type OrderRepository interface {
	Create(order *Order) error
	GetByID(id uint64) (*Order, error)
	GetByOrderID(orderID string) (*Order, error)
	GetByUserID(userID string, limit, offset int) ([]*Order, error)
	GetByPSPPaymentID(pspPaymentID string) (*Order, error)
	Update(order *Order) error
	UpdateStatus(orderID string, payStatus string) error
	SoftDelete(orderID string) error
	List(limit, offset int) ([]*Order, error)
	Count() (int64, error)
	CountByUserID(userID string) (int64, error)

	// 事务方法
	CreateWithTransaction(tx any, order *Order) error
	GetByIDWithTransaction(tx any, id uint64) (*Order, error)
	GetByOrderIDWithTransaction(tx any, orderID string) (*Order, error)
	UpdateWithTransaction(tx any, order *Order) error
	UpdateStatusWithTransaction(tx any, orderID string, payStatus string) error
	SoftDeleteWithTransaction(tx any, orderID string) error
}

// OrderService 订单服务接口
type OrderService interface {
	CreateOrder(userCtx *middleware.UserContext, req *CreateOrderRequest) (*CreateOrderResponse, error)
	GetOrder(orderID string) (*Order, error)
	GetOrderByID(id uint64) (*Order, error)
	GetUserOrders(userID string, limit, offset int) ([]*Order, error)
	UpdateOrder(orderID string, req *UpdateOrderRequest) error
	ProcessWebhook(provider string, payload []byte, signature string) error
	CancelOrder(orderID string) error
	RefundOrder(orderID string, amount *float64) error
}

// PaymentGateway 支付网关接口
type PaymentGateway interface {
	CreateCheckoutSession(order *Order, successURL, cancelURL string) (string, error) // 返回checkout URL
	GetPaymentStatus(pspPaymentID string) (string, error)
	RefundPayment(pspPaymentID string, amount float64) error
	VerifyWebhook(payload []byte, signature string) error
}

// NewOrder 创建新的订单实体
func NewOrder(userID string, req *CreateOrderRequest) *Order {
	now := time.Now()
	return &Order{
		UserID:       userID,
		ProductID:    req.ProductID,
		ProductDesc:  req.ProductDesc,
		PriceID:      req.PriceID,
		Quantity:     req.Quantity,
		Currency:     req.Currency,
		PayStatus:    PayStatusCreated,
		PayedMethod:  req.PayedMethod,
		PSPProvider:  req.PSPProvider,
		RefundStatus: RefundStatusNone,
		CreatedAt:    now,
		UpdatedAt:    now,
		Deleted:      false,
	}
}

// IsPaymentCompleted 检查支付是否完成
func (o *Order) IsPaymentCompleted() bool {
	return o.PayStatus == PayStatusSucceeded || o.PayStatus == PayStatusPaid
}

// IsRefundable 检查是否可以退款
func (o *Order) IsRefundable() bool {
	return o.IsPaymentCompleted() && o.RefundStatus == RefundStatusNone
}

// IsCancellable 检查是否可以取消
func (o *Order) IsCancellable() bool {
	return o.PayStatus == PayStatusCreated || o.PayStatus == PayStatusPaid
}

// MarkAsPaid 标记为已支付
func (o *Order) MarkAsPaid(pspPaymentID, pspCustomerID, pspCustomerEmail string) {
	now := time.Now()
	o.PayStatus = PayStatusSucceeded
	o.PayedAt = &now
	o.PSPPaymentID = pspPaymentID
	o.PSPCustomerID = pspCustomerID
	o.PSPCustomerEmail = pspCustomerEmail
	o.UpdatedAt = now
}

// MarkAsFailed 标记为支付失败
func (o *Order) MarkAsFailed() {
	o.PayStatus = PayStatusFailed
	o.UpdatedAt = time.Now()
}

// MarkAsCancelled 标记为已取消
func (o *Order) MarkAsCancelled() {
	o.PayStatus = PayStatusCancelled
	o.UpdatedAt = time.Now()
}

// MarkAsRefunded 标记为已退款
func (o *Order) MarkAsRefunded(refundAmount float64) {
	now := time.Now()
	o.RefundStatus = RefundStatusSucceeded
	o.RefundedAt = &now
	o.NetAmount = o.Amount - refundAmount
	o.UpdatedAt = now
}

// SoftDelete 软删除订单
func (o *Order) SoftDelete() {
	now := time.Now()
	o.Deleted = true
	o.DeletedAt = &now
	o.UpdatedAt = now
}
