package main

import (
	"fmt"
	"os"
	"payment-backend/internal/config"
	"strings"
)

func main() {
	// 模拟测试环境
	testFromInternalConfig()
}

func testFromInternalConfig() {
	fmt.Println("=== Testing from internal/config directory ===")

	// 切换到 internal/config 目录（模拟测试运行环境）
	originalDir, _ := os.Getwd()
	defer os.Chdir(originalDir)

	os.Chdir("internal/config")

	// 清理环境变量
	os.Unsetenv("PAYMENT_ENV")
	os.Unsetenv("GO_ENV")

	// 设置开发环境
	os.Setenv("PAYMENT_ENV", "dev")
	defer os.Unsetenv("PAYMENT_ENV")

	fmt.Printf("Current working directory: %s\n", getCurrentDir())
	fmt.Printf("Environment: %s\n", os.Getenv("PAYMENT_ENV"))

	// 检查配置文件是否存在
	checkConfigFiles()

	// 切换回项目根目录
	currentDir, _ := os.Getwd()
	if strings.Contains(currentDir, "internal/config") {
		fmt.Println("Switching back to project root...")
		err := os.Chdir("../..")
		if err != nil {
			fmt.Printf("Error changing directory: %v\n", err)
		}
		fmt.Printf("New working directory: %s\n", getCurrentDir())
		checkConfigFiles()
	}

	// 加载配置
	cfg, err := config.LoadConfig("")
	if err != nil {
		fmt.Printf("Error loading config: %v\n", err)
		return
	}

	fmt.Println("=== Configuration Values ===")
	fmt.Printf("Server Mode: %s\n", cfg.Server.Mode)
	fmt.Printf("Database Driver: %s\n", cfg.Database.Driver)
	fmt.Printf("Database Host: %s\n", cfg.Database.Host)
	fmt.Printf("Log Level: %s\n", cfg.Log.Level)
	fmt.Printf("Log Format: %s\n", cfg.Log.Format)
}

func getCurrentDir() string {
	dir, err := os.Getwd()
	if err != nil {
		return "unknown"
	}
	return dir
}

func checkConfigFiles() {
	files := []string{
		"./configs/config.yaml",
		"./configs/config.dev.yaml",
		"./configs/config.prod.yaml",
		"config.yaml",
		"config.dev.yaml",
		"config.prod.yaml",
	}

	fmt.Println("=== Checking Config Files ===")
	for _, file := range files {
		if _, err := os.Stat(file); err == nil {
			fmt.Printf("✓ %s exists\n", file)
		} else {
			fmt.Printf("✗ %s not found\n", file)
		}
	}
}
